#import "SettingTableViewCell.h"

@implementation SettingTableViewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style
              reuseIdentifier:(NSString *)reuseIdentifier {
  self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
  if (self) {
    [self loadCellView];
    [self setSelectionStyle:(UITableViewCellSelectionStyleNone)];
  }
  return self;
}

- (void)loadCellView {
  if (!_iconImageView) {
    _iconImageView = [[UIImageView alloc] init];
    _iconImageView.contentMode = UIViewContentModeScaleAspectFit;
    [self.contentView addSubview:_iconImageView];
  }
  if (!_nameLabel) {
    _nameLabel = [[UILabel alloc] init];
  }
  [_nameLabel setTextColor:KColor_HighBlack];
  [_nameLabel setLineBreakMode:NSLineBreakByWordWrapping];
  _nameLabel.font = SourceHanSerifMediumFont(14 * kMainTemp);
  [self.contentView addSubview:self.nameLabel];
  if (!_mainSwitch) {
    _mainSwitch = [[UISwitch alloc] initWithFrame:CGRectMake(200, 100, 0, 0)];
    _mainSwitch.transform = CGAffineTransformMakeScale(0.8, 0.8);
    _mainSwitch.onTintColor = KColor_HighBlack;
    [self updateSwitchColor];
  }
  [self.contentView addSubview:_mainSwitch];
  if (!_lineLabel) {
    _lineLabel = [[UILabel alloc] init];
  }
  if (!_detailLabel) {
    _detailLabel = [[UILabel alloc] init];
    _detailLabel.textColor = KColor_detailLightGray;
    _detailLabel.font = SourceHanSerifRegularFont(12 * kMainTemp);
    _detailLabel.textAlignment = NSTextAlignmentRight;
    [self.contentView addSubview:_detailLabel];
  }
  if (!_leftButton) {
    _leftButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [_leftButton setTitle:@"" forState:UIControlStateNormal];
    [_leftButton setImage:[UIImage imageNamed:@"normal_black"]
                 forState:UIControlStateNormal];
    [_leftButton setImage:[UIImage imageNamed:@"normal_white"]
                 forState:UIControlStateSelected];
    _leftButton.imageView.contentMode = UIViewContentModeScaleAspectFit;
    [_leftButton setTitleColor:KColor_HighBlack forState:UIControlStateNormal];
    [_leftButton setTitleColor:[UIColor whiteColor]
                      forState:UIControlStateSelected];
    _leftButton.layer.borderColor = KColor_HighBlack.CGColor;
    _leftButton.layer.borderWidth = 1.0;
    [self.contentView addSubview:_leftButton];
    _leftButton.contentEdgeInsets = UIEdgeInsetsMake(
        4 * kMainTemp, 10 * kMainTemp, 4 * kMainTemp, 6 * kMainTemp);
  }
  if (!_rightButton) {
    _rightButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [_rightButton setTitle:@"" forState:UIControlStateNormal];
    [_rightButton setImage:[UIImage imageNamed:@"nine_black"]
                  forState:UIControlStateNormal];
    [_rightButton setImage:[UIImage imageNamed:@"nine_white"]
                  forState:UIControlStateSelected];
    _rightButton.imageView.contentMode = UIViewContentModeScaleAspectFit;
    [_rightButton setTitleColor:KColor_HighBlack forState:UIControlStateNormal];
    [_rightButton setTitleColor:[UIColor whiteColor]
                       forState:UIControlStateSelected];
    _rightButton.layer.borderColor = KColor_HighBlack.CGColor;
    _rightButton.layer.borderWidth = 1.0;
    [self.contentView addSubview:_rightButton];
    _rightButton.contentEdgeInsets = UIEdgeInsetsMake(
        4 * kMainTemp, 2 * kMainTemp, 4 * kMainTemp, 6 * kMainTemp);
  }
}

- (void)loadSetting:(NSInteger)indexrow {
  // 设置图标约束
  [_iconImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.contentView).offset(24 * kMainTemp);
    make.centerY.equalTo(self.contentView);
    make.size.mas_equalTo(CGSizeMake(20 * kMainTemp, 20 * kMainTemp));
  }];

  if (indexrow == 0) {
    [self.nameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.iconImageView.mas_right).offset(12 * kMainTemp);
      make.top.equalTo(self.contentView).offset(11.5 * kMainTemp);
      make.size.mas_equalTo(CGSizeMake(kMainWidth * 0.55, 14.5 * kMainTemp));
    }];
    [_mainSwitch mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.right.equalTo(self.contentView.mas_right).offset(-24 * kMainTemp);
      make.centerY.equalTo(self.nameLabel);
    }];
    _lineLabel.textColor = KColor_textTinyGray;
    _lineLabel.text = @"展示地理位置，需要去 设置-app-52hz-位置 中设置";
    _lineLabel.font = [UIFont systemFontOfSize:12 * kMainTemp];
    [self.contentView addSubview:_lineLabel];
    [_lineLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.contentView).offset(24 * kMainTemp);
      make.top.equalTo(self.nameLabel.mas_bottom).offset(16 * kMainTemp);
      make.size.mas_equalTo(
          CGSizeMake(kMainWidth - 48 * kMainTemp, 12 * kMainTemp));
    }];
    _leftButton.hidden = YES;
    _rightButton.hidden = YES;
  } else if (indexrow == 1) {
    [_lineLabel removeFromSuperview];
    [self.nameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.iconImageView.mas_right).offset(12 * kMainTemp);
      make.centerY.equalTo(self.contentView);
      make.size.mas_equalTo(CGSizeMake(kMainWidth * 0.55, 14.5 * kMainTemp));
    }];
    _mainSwitch.hidden = YES;
    _detailLabel.hidden = YES;
    _leftButton.hidden = NO;
    _rightButton.hidden = NO;
    [_leftButton mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.right.equalTo(self.rightButton.mas_left);
      make.centerY.equalTo(self.contentView);
      make.size.mas_equalTo(CGSizeMake(40 * kMainTemp, 24 * kMainTemp));
    }];
    _leftButton.layer.cornerRadius = 12 * kMainTemp;
    _leftButton.layer.maskedCorners =
        kCALayerMinXMinYCorner | kCALayerMinXMaxYCorner;
    [_rightButton mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.right.equalTo(self.contentView.mas_right).offset(-24 * kMainTemp);
      make.centerY.equalTo(self.contentView);
      make.size.mas_equalTo(CGSizeMake(40 * kMainTemp, 24 * kMainTemp));
    }];
    _rightButton.layer.cornerRadius = 12 * kMainTemp;
    _rightButton.layer.maskedCorners =
        kCALayerMaxXMinYCorner | kCALayerMaxXMaxYCorner;
  } else if (indexrow == 2) {
    [self.nameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.iconImageView.mas_right).offset(12 * kMainTemp);
      make.top.equalTo(self.contentView).offset(11.5 * kMainTemp);
      make.size.mas_equalTo(CGSizeMake(kMainWidth * 0.55, 14.5 * kMainTemp));
    }];
    _lineLabel.textColor = KColor_textTinyGray;
    _lineLabel.text = @"开启后其他用户访问你主页的时候将无法浏览动态列表";
    _lineLabel.font = [UIFont systemFontOfSize:12 * kMainTemp];
    [self.contentView addSubview:_lineLabel];
    [_lineLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.contentView).offset(24 * kMainTemp);
      make.top.equalTo(self.nameLabel.mas_bottom).offset(16 * kMainTemp);
      make.size.mas_equalTo(
          CGSizeMake(kMainWidth - 48 * kMainTemp, 12 * kMainTemp));
    }];
    _leftButton.hidden = YES;
    _rightButton.hidden = YES;
    _detailLabel.hidden = YES;
  } else {
    [_lineLabel removeFromSuperview];
    [self.nameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.iconImageView.mas_right).offset(12 * kMainTemp);
      make.centerY.equalTo(self.contentView);
      make.size.mas_equalTo(CGSizeMake(kMainWidth * 0.55, 14.5 * kMainTemp));
    }];
    if (indexrow == 7) {
      _mainSwitch.hidden = YES;
      [_detailLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.contentView.mas_right).offset(-24 * kMainTemp);
        make.centerY.equalTo(self.nameLabel);
        make.size.mas_equalTo(CGSizeMake(100 * kMainTemp, 14.5 * kMainTemp));
      }];
      _detailLabel.hidden = NO;
    } else {
      _detailLabel.hidden = YES;
      [_mainSwitch mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.contentView.mas_right).offset(-24 * kMainTemp);
        make.centerY.equalTo(self.nameLabel).offset(6 * kMainTemp);
      }];
    }
    _leftButton.hidden = YES;
    _rightButton.hidden = YES;
  }
}

- (void)awakeFromNib {
  [super awakeFromNib];
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
  [super setSelected:selected animated:animated];
}

- (void)prepareForReuse {
  [super prepareForReuse];
  self.iconImageView.image = nil;
  self.nameLabel.text = nil;
  self.detailLabel.text = nil;
  self.mainSwitch.on = NO;
  self.mainSwitch.hidden = NO;
  self.detailLabel.hidden = YES;
  [self.lineLabel removeFromSuperview];
  self.leftButton.hidden = YES;
  self.rightButton.hidden = YES;
  self.leftButton.selected = NO;
  self.rightButton.selected = NO;
  self.leftButton.backgroundColor = [UIColor clearColor];
  self.rightButton.backgroundColor = [UIColor clearColor];
  [self.leftButton setTitleColor:KColor_HighBlack
                        forState:UIControlStateNormal];
  [self.rightButton setTitleColor:KColor_HighBlack
                         forState:UIControlStateNormal];
  self.leftButton.layer.borderColor = KColor_HighBlack.CGColor;
  self.rightButton.layer.borderColor = KColor_HighBlack.CGColor;
  if (self.arrowImageView) {
    [self.arrowImageView removeFromSuperview];
    self.arrowImageView = nil;
  }
}

- (void)updateSwitchColor {
  if ([CurrentUser.gender isEqualToString:@"1"]) {
    _mainSwitch.thumbTintColor = KColor_switchLightBlue;
  } else if ([CurrentUser.gender isEqualToString:@"2"]) {
    _mainSwitch.thumbTintColor = KColor_switchLightPink;
  }
}

@end
