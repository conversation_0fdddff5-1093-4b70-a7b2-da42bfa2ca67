#import "BaseJsonModel.h"

NS_ASSUME_NONNULL_BEGIN

@protocol IPStatusModelResult <NSObject>
@end
@interface IPStatusModelResult : BaseJsonModel
@property(nonatomic, strong) NSString *locationStatus;
@property(nonatomic, strong) NSString *pushStatus;
@property(nonatomic, strong) NSString *cellStyle;
@property(nonatomic, strong) NSString *homeHide;
@property(nonatomic, strong) NSString *haveMobile;

@end

@interface IPStatusModel : BaseJsonModel
@property(nonatomic, strong) NSArray<IPStatusModelResult *> *data;

+ (void)getIPStatusModel:(success)_success failure:(failure)_failure;

@end

NS_ASSUME_NONNULL_END
