#import "MobileBindModel.h"

@implementation MobileBindModel

#pragma mark - 核心API方法

+ (void)sendVerifyCodeToMobile:(NSString *_Nullable)mobile
                       success:(success)_success
                       failure:(failure)_failure {
  NSMutableDictionary *param = [NSMutableDictionary new];
  if (mobile && mobile.length > 0) {
    [param setObject:mobile forKey:@"mobile"];
  }
  [Http getAsynRequestWithUrl:KURLGetMobileCode
                       params:param.count > 0 ? param : nil
                      success:_success
                      failure:_failure];
}

+ (void)verifyCodeWithMobile:(NSString *_Nullable)mobile
                        code:(NSString *)code
                     success:(success)_success
                     failure:(failure)_failure {
  NSMutableDictionary *param = [NSMutableDictionary new];
  [param setObject:code forKey:@"code"];
  if (mobile && mobile.length > 0) {
    [param setObject:mobile forKey:@"mobile"];
  }
  [Http postAsynRequestWithUrl:KURLPostMobileCodeVerify
                        params:param
                       success:_success
                       failure:_failure];
}

#pragma mark - 便利方法（向后兼容）

+ (void)sendChangeVerifyCode:(success)_success failure:(failure)_failure {
  [self sendVerifyCodeToMobile:nil success:_success failure:_failure];
}

+ (void)verifyCurrentMobile:(NSString *)code
                    success:(success)_success
                    failure:(failure)_failure {
  [self verifyCodeWithMobile:nil code:code success:_success failure:_failure];
}

+ (void)bindMobile:(NSString *)mobile
              code:(NSString *)code
           success:(success)_success
           failure:(failure)_failure {
  [self verifyCodeWithMobile:mobile
                        code:code
                     success:_success
                     failure:_failure];
}

@end
