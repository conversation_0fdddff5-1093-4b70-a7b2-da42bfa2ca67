#import "BaseJsonModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface MobileBindModel : BaseJsonModel

@property(nonatomic, strong) NSArray *data;

+ (void)sendVerifyCodeToMobile:(NSString *_Nullable)mobile
                       success:(success)_success
                       failure:(failure)_failure;

+ (void)verifyCodeWithMobile:(NSString *_Nullable)mobile
                        code:(NSString *)code
                     success:(success)_success
                     failure:(failure)_failure;

+ (void)sendChangeVerifyCode:(success)_success
                     failure:(failure)_failure;

+ (void)verifyCurrentMobile:(NSString *)code
                    success:(success)_success
                    failure:(failure)_failure;

+ (void)bindMobile:(NSString *)mobile
              code:(NSString *)code
           success:(success)_success
           failure:(failure)_failure;

@end

NS_ASSUME_NONNULL_END
